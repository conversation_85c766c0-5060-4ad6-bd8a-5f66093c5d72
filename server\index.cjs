const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");

console.log("Loading environment variables...");
dotenv.config({ path: ".env.local" });

console.log("Loading API routes...");
const apiRoutes = require("./routes/api.cjs");

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.use("/api", apiRoutes);

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({ status: "OK", message: "Server is running" });
});

// Start server
console.log("Starting server...");
app
  .listen(PORT, () => {
    console.log(`API Server running on port ${PORT}`);
  })
  .on("error", (err) => {
    console.error("Server failed to start:", err);
  });
