const express = require("express");
const OpenAI = require("openai");

const router = express.Router();

// POST /api/improve-email
router.post("/", async (req, res) => {
  try {
    const { message } = req.body;

    if (!message || !message.trim()) {
      return res.status(400).json({
        error: "Message is required",
      });
    }

    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      return res.status(500).json({
        error: "OpenAI API key not configured",
      });
    }

    const openai = new OpenAI({
      apiKey: apiKey,
    });

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `You are an expert email writing assistant. Your task is to improve the given email draft by making it:

1. More professional and polished
2. Clear and concise
3. Well-structured with proper formatting
4. Appropriate tone for the context
5. Error-free (grammar, spelling, punctuation)

Guidelines:
- Keep the core message and intent intact
- Improve clarity and readability
- Use professional language
- Ensure proper email etiquette
- Make it engaging but not overly casual
- Preserve the original sender's voice as much as possible

Please provide only the improved email without any additional explanations or comments.`,
        },
        {
          role: "user",
          content: `Please improve this email draft:\n\n${message}`,
        },
      ],
      temperature: 0.7,
      max_tokens: 1000,
      stream: true,
    });

    // Set headers for streaming response
    res.setHeader("Content-Type", "text/plain");
    res.setHeader("Transfer-Encoding", "chunked");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");

    // Stream the response
    for await (const chunk of response) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        res.write(content);
      }
    }

    res.end();
  } catch (error) {
    console.error("Error improving email:", error);

    if (!res.headersSent) {
      res.status(500).json({
        error: "Failed to improve email. Please try again.",
      });
    } else {
      res.end();
    }
  }
});

module.exports = router;
