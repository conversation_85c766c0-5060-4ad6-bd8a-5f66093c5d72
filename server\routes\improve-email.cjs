const express = require("express");
const axios = require("axios");

const router = express.Router();

router.post("/", async (req, res) => {
  try {
    const { message, purpose, tone, includeExplanation } = req.body;

    if (!message || !message.trim()) {
      return res.status(400).json({ error: "Message is required" });
    }

    if (!purpose || !purpose.trim()) {
      return res.status(400).json({ error: "Purpose is required" });
    }

    // Create purpose-specific context
    const purposeContext = {
      job: "This is a job-related email. Focus on professionalism, clarity, and appropriate business tone.",
      client:
        "This is a client communication. Emphasize professionalism, courtesy, and clear value proposition.",
      "follow-up":
        "This is a follow-up email. Make it polite, concise, and include a clear call-to-action.",
      apology:
        "This is an apology email. Focus on sincerity, taking responsibility, and offering solutions.",
      complaint:
        "This is a complaint email. Keep it professional, factual, and solution-oriented.",
      custom:
        "This is a general email. Focus on clarity, professionalism, and appropriate tone.",
    };

    const contextInstruction = purposeContext[purpose] || purposeContext.custom;

    // Add tone-specific instructions
    const toneInstructions = {
      friendly:
        "Use a warm, approachable, and friendly tone while maintaining professionalism.",
      formal:
        "Use a formal, business-appropriate tone with proper etiquette and respectful language.",
      confident:
        "Use a confident, assertive tone that conveys authority and expertise.",
      apologetic:
        "Use a sincere, humble, and apologetic tone that takes responsibility and shows empathy.",
    };

    const toneInstruction =
      tone && toneInstructions[tone]
        ? `\n\nTone requirement: ${toneInstructions[tone]}`
        : "";

    // Determine response format based on explanation requirement
    const responseFormat = includeExplanation
      ? `IMPORTANT: You must respond with ONLY a valid JSON object in this exact format:
{
  "improvedEmail": "the improved email content here",
  "explanation": "brief explanation of key changes made"
}

Do not include any text before or after the JSON. Do not use markdown formatting. Return only the raw JSON object.`
      : "Please provide only the improved email without any additional explanations or comments.";

    const systemMessage = `You are an expert email writing assistant. Your task is to improve the given email draft by making it:
1. More professional and polished
2. Clear and concise
3. Well-structured with proper formatting
4. Appropriate tone for the context
5. Error-free (grammar, spelling, punctuation)

Context: ${contextInstruction}${toneInstruction}

Guidelines:
- Keep the core message and intent intact
- Improve clarity and readability
- Use professional language
- Ensure proper email etiquette
- Make it engaging but not overly casual
- Preserve the original sender's voice as much as possible

${responseFormat}`;

    console.log("systemMessage", systemMessage);

    const response = await axios.post(
      "https://api.groq.com/openai/v1/chat/completions",
      {
        model: process.env.MODEL_NAME,
        messages: [
          {
            role: "system",
            content: systemMessage,
          },
          {
            role: "user",
            content: `Please improve this ${purpose} email draft:\n\n${message}`,
          },
        ],
        temperature: 0.7,
        stream: false,
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.GROQ_API_KEY}`,
          "Content-Type": "application/json",
        },
      }
    );

    const reply = response.data.choices[0].message.content;

    // Handle JSON response for explanations
    if (includeExplanation) {
      try {
        // Try to parse as JSON first
        const parsedReply = JSON.parse(reply);

        // Validate that it has the expected structure
        if (parsedReply.improvedEmail) {
          res.json({ result: parsedReply });
        } else {
          // JSON parsed but doesn't have expected structure
          console.warn(
            "JSON response missing improvedEmail field:",
            parsedReply
          );
          res.json({
            result: {
              improvedEmail: reply,
              explanation:
                "Unable to generate explanation due to response format issue.",
            },
          });
        }
      } catch (parseError) {
        // If JSON parsing fails, try to extract content intelligently
        console.warn(
          "Failed to parse JSON response, attempting to extract content:",
          parseError.message
        );

        // Try to find if there's any JSON-like content in the response
        const jsonMatch = reply.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          try {
            const extractedJson = JSON.parse(jsonMatch[0]);
            if (extractedJson.improvedEmail) {
              res.json({ result: extractedJson });
              return;
            }
          } catch (secondParseError) {
            console.warn(
              "Failed to parse extracted JSON:",
              secondParseError.message
            );
          }
        }

        // Final fallback: treat entire response as improved email
        res.json({
          result: {
            improvedEmail: reply,
            explanation:
              "Explanation could not be generated due to response format issue.",
          },
        });
      }
    } else {
      res.json({ result: reply });
    }
  } catch (err) {
    console.error(err?.response?.data || err.message);
    res.status(500).json({ error: "Something went wrong with Groq API" });
  }
});

module.exports = router;
