const express = require("express");
const axios = require("axios");

const router = express.Router();

router.post("/", async (req, res) => {
  try {
    const { message, purpose, tone, includeExplanation } = req.body;

    console.log("process.env.MODEL_NAME", process.env.MODEL_NAME);

    if (!message || !message.trim()) {
      return res.status(400).json({ error: "Message is required" });
    }

    if (!purpose || !purpose.trim()) {
      return res.status(400).json({ error: "Purpose is required" });
    }

    // Create purpose-specific context
    const purposeContext = {
      job: "This is a job-related email. Focus on professionalism, clarity, and appropriate business tone.",
      client:
        "This is a client communication. Emphasize professionalism, courtesy, and clear value proposition.",
      "follow-up":
        "This is a follow-up email. Make it polite, concise, and include a clear call-to-action.",
      apology:
        "This is an apology email. Focus on sincerity, taking responsibility, and offering solutions.",
      complaint:
        "This is a complaint email. Keep it professional, factual, and solution-oriented.",
      custom:
        "This is a general email. Focus on clarity, professionalism, and appropriate tone.",
    };

    const contextInstruction = purposeContext[purpose] || purposeContext.custom;

    // Add tone-specific instructions
    const toneInstructions = {
      friendly:
        "Use a warm, approachable, and friendly tone while maintaining professionalism.",
      formal:
        "Use a formal, business-appropriate tone with proper etiquette and respectful language.",
      confident:
        "Use a confident, assertive tone that conveys authority and expertise.",
      apologetic:
        "Use a sincere, humble, and apologetic tone that takes responsibility and shows empathy.",
    };

    const toneInstruction =
      tone && toneInstructions[tone]
        ? `\n\nTone requirement: ${toneInstructions[tone]}`
        : "";

    // Determine response format based on explanation requirement
    const responseFormat = includeExplanation
      ? `Please respond with a JSON object containing:
{
  "improvedEmail": "the improved email content",
  "explanation": "a brief explanation of the key changes made, focusing on improvements in tone, clarity, structure, and professionalism"
}`
      : "Please provide only the improved email without any additional explanations or comments.";

    const response = await axios.post(
      "https://api.groq.com/openai/v1/chat/completions",
      {
        model: process.env.MODEL_NAME,
        messages: [
          {
            role: "system",
            content: `You are an expert email writing assistant. Your task is to improve the given email draft by making it:

1. More professional and polished
2. Clear and concise
3. Well-structured with proper formatting
4. Appropriate tone for the context
5. Error-free (grammar, spelling, punctuation)

Context: ${contextInstruction}${toneInstruction}

Guidelines:
- Keep the core message and intent intact
- Improve clarity and readability
- Use professional language
- Ensure proper email etiquette
- Make it engaging but not overly casual
- Preserve the original sender's voice as much as possible

${responseFormat}`,
          },
          {
            role: "user",
            content: `Please improve this ${purpose} email draft:\n\n${message}`,
          },
        ],
        temperature: 0.7,
        stream: false,
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.GROQ_API_KEY}`,
          "Content-Type": "application/json",
        },
      }
    );

    const reply = response.data.choices[0].message.content;

    // Handle JSON response for explanations
    if (includeExplanation) {
      try {
        const parsedReply = JSON.parse(reply);
        res.json({ result: parsedReply });
      } catch (parseError) {
        // If JSON parsing fails, return as simple string
        console.warn(
          "Failed to parse JSON response, returning as string:",
          parseError.message
        );
        res.json({ result: { improvedEmail: reply, explanation: null } });
      }
    } else {
      res.json({ result: reply });
    }
  } catch (err) {
    console.error(err?.response?.data || err.message);
    res.status(500).json({ error: "Something went wrong with Groq API" });
  }
});

module.exports = router;
