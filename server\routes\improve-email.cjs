const express = require("express");
const axios = require("axios");

const router = express.Router();

router.post("/", async (req, res) => {
  try {
    const { message, purpose } = req.body;

    console.log("process.env.MODEL_NAME", process.env.MODEL_NAME);

    if (!message || !message.trim()) {
      return res.status(400).json({ error: "Message is required" });
    }

    if (!purpose || !purpose.trim()) {
      return res.status(400).json({ error: "Purpose is required" });
    }

    // Create purpose-specific context
    const purposeContext = {
      job: "This is a job-related email. Focus on professionalism, clarity, and appropriate business tone.",
      client:
        "This is a client communication. Emphasize professionalism, courtesy, and clear value proposition.",
      "follow-up":
        "This is a follow-up email. Make it polite, concise, and include a clear call-to-action.",
      apology:
        "This is an apology email. Focus on sincerity, taking responsibility, and offering solutions.",
      complaint:
        "This is a complaint email. Keep it professional, factual, and solution-oriented.",
      custom:
        "This is a general email. Focus on clarity, professionalism, and appropriate tone.",
    };

    const contextInstruction = purposeContext[purpose] || purposeContext.custom;

    const response = await axios.post(
      "https://api.groq.com/openai/v1/chat/completions",
      {
        model: process.env.MODEL_NAME,
        messages: [
          {
            role: "system",
            content: `You are an expert email writing assistant. Your task is to improve the given email draft by making it:

1. More professional and polished
2. Clear and concise
3. Well-structured with proper formatting
4. Appropriate tone for the context
5. Error-free (grammar, spelling, punctuation)

Context: ${contextInstruction}

Guidelines:
- Keep the core message and intent intact
- Improve clarity and readability
- Use professional language
- Ensure proper email etiquette
- Make it engaging but not overly casual
- Preserve the original sender's voice as much as possible

Please provide only the improved email without any additional explanations or comments.`,
          },
          {
            role: "user",
            content: `Please improve this ${purpose} email draft:\n\n${message}`,
          },
        ],
        temperature: 0.7,
        stream: false,
      },
      {
        headers: {
          Authorization: `Bearer ${process.env.GROQ_API_KEY}`,
          "Content-Type": "application/json",
        },
      }
    );

    const reply = response.data.choices[0].message.content;
    res.json({ result: reply });
  } catch (err) {
    console.error(err?.response?.data || err.message);
    res.status(500).json({ error: "Something went wrong with Groq API" });
  }
});

module.exports = router;
