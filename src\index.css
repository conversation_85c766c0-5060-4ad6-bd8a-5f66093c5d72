@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4% 16%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 4% 16%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 4% 16%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 4% 16%;
    --input: 240 4% 16%;
    --ring: 217 91% 60%;

    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%), hsl(225 100% 70%));
    --gradient-secondary: linear-gradient(135deg, hsl(240 4% 16%), hsl(240 6% 20%));
    --gradient-bg: linear-gradient(135deg, hsl(240 10% 3.9%), hsl(240 8% 6%));
    
    --shadow-glow: 0 0 40px hsl(217 91% 60% / 0.3);
    --shadow-card: 0 10px 30px -10px hsl(0 0% 0% / 0.3);
    
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .light {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 5% 96%;
    --secondary-foreground: 240 6% 10%;

    --muted: 240 5% 96%;
    --muted-foreground: 240 4% 46%;

    --accent: 240 5% 96%;
    --accent-foreground: 240 6% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 6% 90%;
    --input: 240 6% 90%;
    --ring: 217 91% 60%;

    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%), hsl(225 100% 70%));
    --gradient-secondary: linear-gradient(135deg, hsl(240 5% 96%), hsl(240 6% 94%));
    --gradient-bg: linear-gradient(135deg, hsl(0 0% 100%), hsl(240 5% 98%));
    
    --shadow-glow: 0 0 40px hsl(217 91% 60% / 0.2);
    --shadow-card: 0 10px 30px -10px hsl(0 0% 0% / 0.1);
    
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}