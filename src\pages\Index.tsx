import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Loader2,
  Mail,
  Sparkles,
  CheckCircle,
  Copy,
  Download,
  AlertTriangle,
  Info,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
// import { improveEmail } from "@/services/openai";
import { improveEmail, EmailImprovementResult } from "@/services/groqai";
import {
  getRemainingUsage,
  incrementUsage,
  isRateLimited,
} from "@/utils/rateLimiting";
import { generateEmailPDF } from "@/utils/pdfGenerator";

const Index = () => {
  const [emailInput, setEmailInput] = useState("");
  const [improvedEmail, setImprovedEmail] = useState("");
  const [explanation, setExplanation] = useState("");
  const [purpose, setPurpose] = useState("");
  const [tone, setTone] = useState("");
  const [includeExplanation, setIncludeExplanation] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [remainingUsage, setRemainingUsage] = useState(3);
  const { toast } = useToast();

  useEffect(() => {
    setRemainingUsage(getRemainingUsage());
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Submitting...", Date.now());

    // Check rate limiting
    if (isRateLimited()) {
      toast({
        title: "Daily limit reached",
        description: `You've reached your daily limit of ${3} emails. Try again tomorrow!`,
        variant: "destructive",
      });
      return;
    }

    if (!emailInput.trim()) {
      toast({
        title: "Please enter an email",
        description: "Add some text to improve your email draft.",
        variant: "destructive",
      });
      return;
    }

    if (!purpose) {
      toast({
        title: "Please select a purpose",
        description: "Choose the purpose of your email to get better results.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    setImprovedEmail("");
    setExplanation("");
    console.log("Calling improveEmail", Date.now());

    try {
      // Increment usage count
      if (!incrementUsage()) {
        toast({
          title: "Daily limit reached",
          description: `You've reached your daily limit of ${3} emails. Try again tomorrow!`,
          variant: "destructive",
        });
        setIsLoading(false);
        return;
      }

      const result = await improveEmail(
        emailInput,
        purpose,
        tone,
        includeExplanation
      );
      setImprovedEmail(result.improvedEmail);
      if (result.explanation) {
        setExplanation(result.explanation);
      }

      // Update remaining usage
      setRemainingUsage(getRemainingUsage());

      toast({
        title: "Email improved!",
        description: "Your email has been enhanced with AI.",
      });
    } catch (error) {
      console.error("Error improving email:", error);
      toast({
        title: "Error",
        description:
          "Failed to improve email. Please check your API key and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(improvedEmail);
    toast({
      title: "Copied!",
      description: "Email copied to clipboard.",
    });
  };

  const handleDownloadPDF = () => {
    generateEmailPDF(improvedEmail, explanation);
    toast({
      title: "PDF Generated!",
      description: "Your improved email has been downloaded as PDF.",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-bg p-4 md:p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="p-3 bg-gradient-primary rounded-xl shadow-glow">
              <Mail className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-blue-400 bg-clip-text text-transparent">
              EmailFix.ai
            </h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Transform your email drafts with AI. Get professional, clear, and
            impactful emails in seconds.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-6">
          {/* Input Section */}
          <Card className="border-primary/20 bg-card/50 backdrop-blur-sm shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-primary" />
                Your Email Draft
              </CardTitle>
              <CardDescription>
                Paste your email draft below and let AI make it better
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Rate Limiting Banner */}
                {remainingUsage <= 1 && (
                  <Alert className="border-orange-200 bg-orange-50">
                    <AlertTriangle className="h-4 w-4 text-orange-600" />
                    <AlertTitle className="text-orange-800">
                      Usage Warning
                    </AlertTitle>
                    <AlertDescription className="text-orange-700">
                      You have {remainingUsage} email
                      {remainingUsage !== 1 ? "s" : ""} remaining today.
                      {remainingUsage === 0 &&
                        " You've reached your daily limit."}
                    </AlertDescription>
                  </Alert>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="purpose" className="text-sm font-medium">
                      Email Purpose
                    </label>
                    <Select value={purpose} onValueChange={setPurpose}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select email purpose" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="job">Job</SelectItem>
                        <SelectItem value="client">Client</SelectItem>
                        <SelectItem value="follow-up">Follow-up</SelectItem>
                        <SelectItem value="apology">Apology</SelectItem>
                        <SelectItem value="complaint">Complaint</SelectItem>
                        <SelectItem value="custom">Custom</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <label htmlFor="tone" className="text-sm font-medium">
                        Tone (Optional)
                      </label>
                      {tone && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => setTone("")}
                          className="h-auto p-1 text-xs text-muted-foreground hover:text-foreground"
                        >
                          Clear
                        </Button>
                      )}
                    </div>
                    <Select value={tone} onValueChange={setTone}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select tone (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="friendly">Friendly</SelectItem>
                        <SelectItem value="formal">Formal</SelectItem>
                        <SelectItem value="confident">Confident</SelectItem>
                        <SelectItem value="apologetic">Apologetic</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="explanation"
                    checked={includeExplanation}
                    onCheckedChange={setIncludeExplanation}
                  />
                  <Label htmlFor="explanation" className="text-sm font-medium">
                    Include explanation of changes
                  </Label>
                </div>
                <Textarea
                  placeholder="Hi John,

I hope this email finds you well. I wanted to reach out about the project we discussed last week. I think we should probably schedule a meeting to go over the details and figure out what the next steps should be.

Let me know when you're available.

Thanks,
[Your name]"
                  value={emailInput}
                  onChange={(e) => setEmailInput(e.target.value)}
                  className="min-h-[300px] bg-background/50 border-border/50 focus:border-primary/50 resize-none"
                />
                <Button
                  type="submit"
                  disabled={isLoading}
                  variant="hero"
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Improving Email...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      Fix My Email
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Output Section */}
          <Card className="border-primary/20 bg-card/50 backdrop-blur-sm shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                Improved Email
              </CardTitle>
              <CardDescription>
                Your AI-enhanced email will appear here
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Improved Email Output */}
                <div className="min-h-[300px] p-4 bg-background/50 rounded-lg border border-border/50">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <Loader2 className="w-8 h-8 animate-spin text-primary mx-auto mb-2" />
                        <p className="text-muted-foreground">
                          Analyzing and improving your email...
                        </p>
                      </div>
                    </div>
                  ) : improvedEmail ? (
                    <div className="space-y-4">
                      <Textarea
                        value={improvedEmail}
                        onChange={(e) => setImprovedEmail(e.target.value)}
                        className="min-h-[250px] bg-transparent border-none resize-none focus:ring-0 p-0 text-foreground leading-relaxed"
                        placeholder="Your improved email will appear here..."
                      />
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      <div className="text-center">
                        <Mail className="w-12 h-12 mx-auto mb-2 opacity-50" />
                        <p>Your improved email will appear here</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                {improvedEmail && !isLoading && (
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyToClipboard}
                      className="flex items-center gap-2"
                    >
                      <Copy className="w-4 h-4" />
                      Copy to Clipboard
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleDownloadPDF}
                      className="flex items-center gap-2"
                    >
                      <Download className="w-4 h-4" />
                      Download as PDF
                    </Button>
                  </div>
                )}

                {/* Explanation Section */}
                {explanation && !isLoading && (
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center gap-2 mb-2">
                      <Info className="w-4 h-4 text-blue-600" />
                      <h3 className="font-medium text-blue-900">Explanation</h3>
                    </div>
                    <div className="text-sm text-blue-800 whitespace-pre-wrap leading-relaxed">
                      {explanation}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-muted-foreground space-y-2">
          <div className="flex items-center justify-center gap-2 text-sm">
            <span>Free Tier:</span>
            <span className="font-medium text-foreground">
              {remainingUsage}/3 emails remaining today
            </span>
          </div>
          <p className="text-sm">
            Powered by Groq • Built with React & Tailwind CSS
          </p>
        </div>
      </div>
    </div>
  );
};

export default Index;
