import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2, Mail, Sparkles, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
// import { improveEmail } from "@/services/openai";
import { improveEmail } from "@/services/groqai";

const Index = () => {
  const [emailInput, setEmailInput] = useState("");
  const [improvedEmail, setImprovedEmail] = useState("");
  const [purpose, setPurpose] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Submitting...", Date.now());

    if (!emailInput.trim()) {
      toast({
        title: "Please enter an email",
        description: "Add some text to improve your email draft.",
        variant: "destructive",
      });
      return;
    }

    if (!purpose) {
      toast({
        title: "Please select a purpose",
        description: "Choose the purpose of your email to get better results.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    setImprovedEmail("");
    console.log("Calling improveEmail", Date.now());

    try {
      // For groq - pass purpose as context
      const improved = await improveEmail(emailInput, purpose);
      setImprovedEmail(improved);

      toast({
        title: "Email improved!",
        description: "Your email has been enhanced with AI.",
      });
    } catch (error) {
      console.error("Error improving email:", error);
      toast({
        title: "Error",
        description:
          "Failed to improve email. Please check your API key and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-bg p-4 md:p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="p-3 bg-gradient-primary rounded-xl shadow-glow">
              <Mail className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-blue-400 bg-clip-text text-transparent">
              EmailFix.ai
            </h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Transform your email drafts with AI. Get professional, clear, and
            impactful emails in seconds.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-6">
          {/* Input Section */}
          <Card className="border-primary/20 bg-card/50 backdrop-blur-sm shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-primary" />
                Your Email Draft
              </CardTitle>
              <CardDescription>
                Paste your email draft below and let AI make it better
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="purpose" className="text-sm font-medium">
                    Email Purpose
                  </label>
                  <Select value={purpose} onValueChange={setPurpose}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select email purpose" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="job">Job</SelectItem>
                      <SelectItem value="client">Client</SelectItem>
                      <SelectItem value="follow-up">Follow-up</SelectItem>
                      <SelectItem value="apology">Apology</SelectItem>
                      <SelectItem value="complaint">Complaint</SelectItem>
                      <SelectItem value="custom">Custom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Textarea
                  placeholder="Hi John,

I hope this email finds you well. I wanted to reach out about the project we discussed last week. I think we should probably schedule a meeting to go over the details and figure out what the next steps should be.

Let me know when you're available.

Thanks,
[Your name]"
                  value={emailInput}
                  onChange={(e) => setEmailInput(e.target.value)}
                  className="min-h-[300px] bg-background/50 border-border/50 focus:border-primary/50 resize-none"
                />
                <Button
                  type="submit"
                  disabled={isLoading}
                  variant="hero"
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Improving Email...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      Fix My Email
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Output Section */}
          <Card className="border-primary/20 bg-card/50 backdrop-blur-sm shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                Improved Email
              </CardTitle>
              <CardDescription>
                Your AI-enhanced email will appear here
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="min-h-[300px] p-4 bg-background/50 rounded-lg border border-border/50">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <Loader2 className="w-8 h-8 animate-spin text-primary mx-auto mb-2" />
                      <p className="text-muted-foreground">
                        Analyzing and improving your email...
                      </p>
                    </div>
                  </div>
                ) : improvedEmail ? (
                  <div className="space-y-2">
                    <div className="whitespace-pre-wrap text-foreground leading-relaxed">
                      {improvedEmail}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(improvedEmail);
                        toast({
                          title: "Copied!",
                          description: "Email copied to clipboard.",
                        });
                      }}
                      className="mt-4"
                    >
                      Copy to Clipboard
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <div className="text-center">
                      <Mail className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>Your improved email will appear here</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-muted-foreground">
          <p className="text-sm">
            Powered by Groq • Built with React & Tailwind CSS
          </p>
        </div>
      </div>
    </div>
  );
};

export default Index;
