export interface EmailImprovementResult {
  improvedEmail: string;
  explanation?: string;
}

export async function improveEmail(
  message: string,
  purpose: string,
  tone?: string,
  includeExplanation?: boolean
): Promise<EmailImprovementResult> {
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

  const response = await fetch(`${API_BASE_URL}/api/improve-email`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      message,
      purpose,
      tone,
      includeExplanation,
    }),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  if (!data.result) {
    throw new Error("No improved email returned");
  }

  // Handle both old and new response formats
  if (typeof data.result === "string") {
    return { improvedEmail: data.result };
  }

  return {
    improvedEmail: data.result.improvedEmail || data.result,
    explanation: data.result.explanation,
  };
}
