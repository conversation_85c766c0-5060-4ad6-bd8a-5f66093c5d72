export async function improveEmail(message: string): Promise<ReadableStream> {
  const API_BASE_URL = 'http://localhost:3002';

  const response = await fetch(`${API_BASE_URL}/api/improve-email`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ message }),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  if (!response.body) {
    throw new Error('No response body');
  }

  return response.body;
}
