import html2pdf from 'html2pdf.js';

export function generateEmailPDF(emailContent: string, explanation?: string): void {
  const currentDate = new Date().toLocaleDateString();
  
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin-bottom: 10px;">EmailFix.ai</h1>
        <p style="color: #6b7280; margin: 0;">Improved Email - ${currentDate}</p>
      </div>
      
      <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #1f2937; margin-top: 0; margin-bottom: 15px; font-size: 18px;">Improved Email</h2>
        <div style="white-space: pre-wrap; line-height: 1.6; color: #374151;">
          ${emailContent}
        </div>
      </div>
      
      ${explanation ? `
        <div style="background: #fef3c7; padding: 20px; border-radius: 8px; border-left: 4px solid #f59e0b;">
          <h2 style="color: #92400e; margin-top: 0; margin-bottom: 15px; font-size: 18px;">Explanation</h2>
          <div style="white-space: pre-wrap; line-height: 1.6; color: #78350f;">
            ${explanation}
          </div>
        </div>
      ` : ''}
      
      <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
        <p style="color: #9ca3af; font-size: 12px; margin: 0;">
          Generated by EmailFix.ai - Transform your emails with AI
        </p>
      </div>
    </div>
  `;

  const options = {
    margin: 1,
    filename: 'Improved-Email.pdf',
    image: { type: 'jpeg', quality: 0.98 },
    html2canvas: { scale: 2 },
    jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
  };

  html2pdf().set(options).from(htmlContent).save();
}
