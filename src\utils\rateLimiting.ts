const RATE_LIMIT_KEY = 'email_fix_usage';
const DAILY_LIMIT = 3;

export interface UsageData {
  count: number;
  date: string;
}

export function getRemainingUsage(): number {
  const today = new Date().toDateString();
  const stored = localStorage.getItem(RATE_LIMIT_KEY);
  
  if (!stored) {
    return DAILY_LIMIT;
  }
  
  try {
    const usage: UsageData = JSON.parse(stored);
    
    // Reset count if it's a new day
    if (usage.date !== today) {
      return DAILY_LIMIT;
    }
    
    return Math.max(0, DAILY_LIMIT - usage.count);
  } catch {
    return DAILY_LIMIT;
  }
}

export function incrementUsage(): boolean {
  const today = new Date().toDateString();
  const stored = localStorage.getItem(RATE_LIMIT_KEY);
  
  let usage: UsageData = { count: 0, date: today };
  
  if (stored) {
    try {
      const parsedUsage: UsageData = JSON.parse(stored);
      
      // Reset count if it's a new day
      if (parsedUsage.date === today) {
        usage = parsedUsage;
      }
    } catch {
      // Invalid data, use default
    }
  }
  
  if (usage.count >= DAILY_LIMIT) {
    return false; // Rate limit exceeded
  }
  
  usage.count += 1;
  localStorage.setItem(RATE_LIMIT_KEY, JSON.stringify(usage));
  
  return true;
}

export function isRateLimited(): boolean {
  return getRemainingUsage() <= 0;
}
